export interface ModelUpdateInfo {
  id: string
  name: string
  description?: string
  context_length: number
  pricing: {
    prompt: string
    completion: string
  }
  top_provider: {
    max_completion_tokens?: number
  }
  architecture?: {
    modality?: string
    tokenizer?: string
    instruct_type?: string
  }
  per_request_limits?: {
    prompt_tokens?: string
    completion_tokens?: string
  }
  // Metadata for updates
  added_date?: string
  updated_date?: string
  deprecated?: boolean
  replacement_model?: string
}

export interface ModelUpdateManifest {
  version: string
  last_updated: string
  models: ModelUpdateInfo[]
  deprecated_models: string[]
  featured_models: string[]
}

class ModelUpdateService {
  private updateUrl = '/models-manifest-example.json' // Static file in public folder
  private fallbackUrl = 'https://openrouter.ai/api/v1/models'
  
  // Fetch latest model manifest from your server
  async fetchModelManifest(): Promise<ModelUpdateManifest | null> {
    try {
      const response = await fetch(this.updateUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.warn('Failed to fetch model manifest, falling back to OpenRouter')
        return null
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching model manifest:', error)
      return null
    }
  }

  // Get models with OTA updates
  async getUpdatedModels(apiKey: string): Promise<ModelUpdateInfo[]> {
    // Try to get updated manifest first
    const manifest = await this.fetchModelManifest()
    
    if (manifest) {
      console.log(`Using model manifest v${manifest.version} (${manifest.last_updated})`)
      return manifest.models.filter(model => !model.deprecated)
    }

    // Fallback to OpenRouter API
    console.log('Using OpenRouter API fallback')
    return this.fetchFromOpenRouter(apiKey)
  }

  // Fallback to OpenRouter API
  private async fetchFromOpenRouter(apiKey: string): Promise<ModelUpdateInfo[]> {
    try {
      const response = await fetch(this.fallbackUrl, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.statusText}`)
      }

      const data = await response.json()
      return data.data || []
    } catch (error) {
      console.error('Error fetching from OpenRouter:', error)
      return []
    }
  }

  // Check if local model cache needs update
  async shouldUpdateModels(): Promise<{ shouldUpdate: boolean; latestVersion?: string }> {
    try {
      const manifest = await this.fetchModelManifest()
      if (!manifest) return { shouldUpdate: false }

      // Get stored version from database
      const storedVersion = await this.getStoredModelVersion()

      console.log(`Model version check: stored=${storedVersion}, latest=${manifest.version}`)

      return {
        shouldUpdate: manifest.version !== storedVersion,
        latestVersion: manifest.version
      }
    } catch (error) {
      console.error('Error checking model updates:', error)
      return { shouldUpdate: false }
    }
  }

  // Force update models (for testing)
  async forceUpdateModels(): Promise<void> {
    try {
      // Clear stored version to force update
      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('model-manifest-version', '')
      }
      console.log('Forced model update - cleared version cache')
    } catch (error) {
      console.error('Error forcing model update:', error)
    }
  }

  // Store model version in database
  private async getStoredModelVersion(): Promise<string | null> {
    try {
      if (window.electronAPI?.settings) {
        return await window.electronAPI.settings.get('model-manifest-version')
      }
      return null
    } catch (error) {
      console.error('Error getting stored model version:', error)
      return null
    }
  }

  // Update stored model version
  async updateStoredModelVersion(version: string): Promise<void> {
    try {
      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('model-manifest-version', version)
      }
    } catch (error) {
      console.error('Error updating stored model version:', error)
    }
  }

  // Cache models in database for offline access
  async cacheModelsInDatabase(models: ModelUpdateInfo[]): Promise<void> {
    try {
      if (window.electronAPI?.db) {
        // Store models as JSON in database
        await window.electronAPI.settings.set('cached-models', JSON.stringify(models))
        await window.electronAPI.settings.set('models-cache-timestamp', Date.now().toString())
      }
    } catch (error) {
      console.error('Error caching models in database:', error)
    }
  }

  // Get cached models from database
  async getCachedModels(): Promise<ModelUpdateInfo[]> {
    try {
      if (window.electronAPI?.settings) {
        const cachedModels = await window.electronAPI.settings.get('cached-models')
        if (cachedModels) {
          return JSON.parse(cachedModels)
        }
      }
      return []
    } catch (error) {
      console.error('Error getting cached models:', error)
      return []
    }
  }

  // Check if cache is stale (older than 24 hours)
  async isCacheStale(): Promise<boolean> {
    try {
      if (window.electronAPI?.settings) {
        const timestamp = await window.electronAPI.settings.get('models-cache-timestamp')
        if (timestamp) {
          const cacheAge = Date.now() - parseInt(timestamp)
          return cacheAge > 24 * 60 * 60 * 1000 // 24 hours
        }
      }
      return true
    } catch (error) {
      console.error('Error checking cache staleness:', error)
      return true
    }
  }
}

export const modelUpdateService = new ModelUpdateService()
