# ChatLo Model Update System

A comprehensive system for managing AI model updates, categorization, and OTA (Over-The-Air) updates.

## 📁 Structure

```
modelUpdate/
├── README.md              # This file
├── package.json           # Node.js dependencies
├── modelCrawler.js        # OpenRouter API crawler
├── generateManifest.js    # Manifest generator
├── updateLogic.ts         # Core update logic (TypeScript)
└── models-manifest.json   # Generated model manifest
```

## 🎯 Features

### 1. **Dynamic Model Categorization**
- **Flagship Models**: Latest and largest from major providers
- **Free Models**: Zero-cost models
- **Vision Models**: Multimodal image support
- **Reasoning Models**: Chain-of-thought capabilities
- **Code Models**: Programming-focused models

### 2. **Flagship Model Criteria**
Automatically identifies flagship models from:
- **OpenAI**: GPT-4o, o1-preview, o1-mini, ChatGPT-4o-latest
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Opus
- **Google**: Gemini 2.5 Pro/Flash, Gemini Pro 1.5
- **xAI**: Grok 4, <PERSON>rok 3
- **Meta**: Llama 3.3 70B, Llama 3.1 405B/70B
- **DeepSeek**: DeepSeek R1, DeepSeek V3
- **Qwen**: Qwen 2.5 72B, Qwen Max

### 3. **OTA Update System**
- **Version-based updates**: Automatic detection of new manifest versions
- **Database caching**: Offline model access
- **Graceful fallback**: OpenRouter API backup
- **Toast notifications**: User feedback on updates

## 🚀 Usage

### Backend Model Crawling
```bash
cd modelUpdate
npm install
node modelCrawler.js
```

### Frontend Integration
```typescript
import { modelUpdateLogic } from './modelUpdate/updateLogic'

// Check for updates
const { shouldUpdate, latestVersion } = await modelUpdateLogic.shouldUpdate()

// Get models (with OTA support)
const models = await modelUpdateLogic.getModels(apiKey)

// Force update (testing)
await modelUpdateLogic.forceUpdate()
```

## 📋 Model Manifest Schema

```json
{
  "version": "YYYY.MM.DD",
  "last_updated": "ISO timestamp",
  "crawl_timestamp": "Unix timestamp",
  "statistics": {
    "total_models": 123,
    "flagship_models": 15,
    "free_models": 8,
    "vision_models": 25,
    "reasoning_models": 12,
    "code_models": 18,
    "providers": 7
  },
  "models": [
    {
      "id": "provider/model-name",
      "name": "Display Name",
      "description": "Model description",
      "context_length": 128000,
      "pricing": {
        "prompt": "0.000005",
        "completion": "0.000015"
      },
      "top_provider": {
        "max_completion_tokens": 16384
      },
      "architecture": {
        "modality": "text+image->text",
        "tokenizer": "GPT",
        "instruct_type": "chatml"
      },
      "provider": "openai",
      "categories": ["flagship", "vision"],
      "is_flagship": true,
      "is_free": false,
      "created_timestamp": **********,
      "last_updated": "2024-07-17T10:30:00Z"
    }
  ],
  "featured_models": ["provider/model1", "provider/model2"],
  "deprecated_models": ["old/model"],
  "metadata": {
    "source": "OpenRouter API",
    "generator_version": "1.0"
  }
}
```

## 🔄 Update Process

1. **Crawl OpenRouter API** → Generate new manifest
2. **Update version** → Trigger OTA detection
3. **Deploy manifest** → Static file or CDN
4. **App checks version** → Compare with stored version
5. **Download new models** → Cache in database
6. **Show toast notification** → User feedback

## 🛠️ Configuration

### Update Frequency
- **Manual**: Run crawler script
- **Automated**: Set up cron job/GitHub Actions
- **On-demand**: API endpoint trigger

### Flagship Criteria Customization
Edit `flagshipCriteria` in `updateLogic.ts`:
```typescript
private flagshipCriteria = {
  'provider': {
    patterns: ['model-pattern-1', 'model-pattern-2'],
    exclude: ['old-model', 'deprecated-model']
  }
}
```

## 📊 Statistics Tracking

The system automatically tracks:
- Total model count
- Models by category
- Provider distribution
- Update timestamps
- Version history

## 🔧 Testing

### Force Update
```typescript
await modelUpdateLogic.forceUpdate()
```

### Version Check
```typescript
const { shouldUpdate, latestVersion } = await modelUpdateLogic.shouldUpdate()
console.log(`Should update: ${shouldUpdate}, Latest: ${latestVersion}`)
```

## 🚀 Deployment

1. **Generate manifest**: Run crawler
2. **Update version**: Increment in manifest
3. **Deploy file**: Upload to CDN/static hosting
4. **App auto-detects**: Next model load triggers update

## 📝 Notes

- **Claude 4**: Not yet available on OpenRouter (as of July 2024)
- **GPT-4.1**: Not confirmed - may be GPT-4o variants
- **Version format**: YYYY.MM.DD for easy comparison
- **Caching**: 24-hour cache expiry for performance
- **Fallback**: Always graceful degradation to OpenRouter API
